import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

data = pd.read_csv('cat_dog.csv')

data.head()

data = np.array(data)
m, n = data.shape
np.random.shuffle(data)

m,n

train_data = data[:int(0.8 * m), :]
val_data = data[int(0.8 * m): , :]

# X = all columns except last, y = last column
X_train = train_data[:, :-1].T
X_train = X_train / 255.0  # Normalize pixel values to [0, 1]
y_train = train_data[:, -1]

X_val = val_data[:, :-1].T
X_val = X_val / 255.0  # Normalize pixel values to [0, 1]
y_val = val_data[:, -1]


print("X_train shape:", X_train.shape)
print("y_train shape:", y_train.shape)
print("X_val shape:", X_val.shape)
print("y_val shape:", y_val.shape)

def initialize_parameters():
    W1 = np.random.rand(64, 4096) - 0.5
    B1 = np.random.rand(64, 1) - 0.5
    W2 = np.random.rand(2, 64) - 0.5
    B2 = np.random.rand(2, 1) - 0.5
    return W1, B1, W2, B2

def ReLU(X):
    return np.maximum(0, X)

def softmax_calculator(Z):
    # Update softmax for numerical stability
    expZ = np.exp(Z - np.max(Z, axis=0, keepdims=True))
    return expZ / np.sum(expZ, axis=0, keepdims=True)


def forward_propagation(W1 , B1, W2, B2, X):
    Z1 = W1.dot(X) + B1
    A1 = ReLU(Z1)
    Z2 = W2.dot(A1) + B2
    A2 = softmax_calculator(Z2)
    return Z1, A1, Z2, A2


def one_hot_converter(Y):
    one_hot_Y = np.zeros((Y.size, Y.max() +1))
    one_hot_Y[np.arange(Y.size), Y] = 1
    return one_hot_Y.T

def backward_propagation(W1, B1, W2, B2, Z1, A1, Z2, A2, Y, X):
    one_hot_Y = one_hot_converter(Y)
    dZ2 = A2 - one_hot_Y
    dW2 = 1/m * dZ2.dot(A1.T)
    dB2 = 1/m * np.sum(dZ2, axis=1, keepdims=True)
    dZ1 = W2.T.dot(dZ2) * (Z1 > 0)
    dW1 = 1/m * dZ1.dot(X.T)
    dB1 = 1/m * np.sum(dZ1, axis=1, keepdims=True)
    return dW1, dB1, dW2, dB2

def update_parameters(W1, B1, W2, B2, dW1, dB1, dW2, dB2, learning_rate=0.01):
    W1 = W1 - learning_rate * dW1
    B1 = B1 - learning_rate * dB1
    W2 = W2 - learning_rate * dW2
    B2 = B2 - learning_rate * dB2
    return W1, B1, W2, B2

def get_predictions(A2):
    return np.argmax(A2, 0)
def get_accuracy(predictions, Y):
    if Y.ndim > 1:
        Y = np.argmax(Y, 0)
    return np.sum(predictions == Y) / Y.size



def gradient_descent(X, Y , alpha, iterations):
    W1, B1, W2, B2 = initialize_parameters()
    for i in range(iterations):  
        Z1, A1, Z2, A2 = forward_propagation(W1, B1, W2, B2, X)
        dW1, dB1, dW2, dB2 = backward_propagation(W1, B1, W2, B2, Z1, A1, Z2, A2, Y, X)
        W1, B1, W2, B2 = update_parameters(W1, B1, W2, B2, dW1, dB1, dW2, dB2, alpha)
        if (i%20) == 0:
            print("Iteration Number : ", i)
            print("Accuracy = ", get_accuracy(get_predictions(A2), Y))
    return W1, B1, W2, B2

W1, B1, W2, B2 = gradient_descent(X_train, y_train, 0.5, 100)

# For validation, use one-hot encoding for accuracy if needed
import random
val_index = random.randint(0, X_val.shape[1] - 1)
Z1val, A1val, Z2val, A2val = forward_propagation(W1, B1, W2, B2, X_val[:, val_index, None])
predictions = get_predictions(A2val)
print("predicted label: ", predictions)
print("actual label: ", y_val[val_index])
image_array = X_val[:, val_index].reshape(64, 64)
plt.imshow(image_array, cmap='gray')
plt.show()