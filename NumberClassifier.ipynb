{"cells": [{"cell_type": "code", "execution_count": 108, "id": "c5596d5b", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 109, "id": "f8bc5a34", "metadata": {}, "outputs": [], "source": ["data = pd.read_csv('train.csv')"]}, {"cell_type": "code", "execution_count": 110, "id": "0004b338", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>label</th>\n", "      <th>pixel0</th>\n", "      <th>pixel1</th>\n", "      <th>pixel2</th>\n", "      <th>pixel3</th>\n", "      <th>pixel4</th>\n", "      <th>pixel5</th>\n", "      <th>pixel6</th>\n", "      <th>pixel7</th>\n", "      <th>pixel8</th>\n", "      <th>...</th>\n", "      <th>pixel774</th>\n", "      <th>pixel775</th>\n", "      <th>pixel776</th>\n", "      <th>pixel777</th>\n", "      <th>pixel778</th>\n", "      <th>pixel779</th>\n", "      <th>pixel780</th>\n", "      <th>pixel781</th>\n", "      <th>pixel782</th>\n", "      <th>pixel783</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 785 columns</p>\n", "</div>"], "text/plain": ["   label  pixel0  pixel1  pixel2  pixel3  pixel4  pixel5  pixel6  pixel7  \\\n", "0      1       0       0       0       0       0       0       0       0   \n", "1      0       0       0       0       0       0       0       0       0   \n", "2      1       0       0       0       0       0       0       0       0   \n", "3      4       0       0       0       0       0       0       0       0   \n", "4      0       0       0       0       0       0       0       0       0   \n", "\n", "   pixel8  ...  pixel774  pixel775  pixel776  pixel777  pixel778  pixel779  \\\n", "0       0  ...         0         0         0         0         0         0   \n", "1       0  ...         0         0         0         0         0         0   \n", "2       0  ...         0         0         0         0         0         0   \n", "3       0  ...         0         0         0         0         0         0   \n", "4       0  ...         0         0         0         0         0         0   \n", "\n", "   pixel780  pixel781  pixel782  pixel783  \n", "0         0         0         0         0  \n", "1         0         0         0         0  \n", "2         0         0         0         0  \n", "3         0         0         0         0  \n", "4         0         0         0         0  \n", "\n", "[5 rows x 785 columns]"]}, "execution_count": 110, "metadata": {}, "output_type": "execute_result"}], "source": ["data.head(5)"]}, {"cell_type": "code", "execution_count": 111, "id": "88dfa52a", "metadata": {}, "outputs": [], "source": ["data = np.array(data)\n", "m, n = data.shape\n", "np.random.shuffle(data)"]}, {"cell_type": "code", "execution_count": 112, "id": "9d236e5e", "metadata": {}, "outputs": [{"data": {"text/plain": ["(42000, 785)"]}, "execution_count": 112, "metadata": {}, "output_type": "execute_result"}], "source": ["m,n"]}, {"cell_type": "code", "execution_count": 113, "id": "a86b68f7", "metadata": {}, "outputs": [], "source": ["train_data = data[:int(0.8 * m), :]\n", "val_data = data[int(0.8 * m): , :]\n", "\n", "X_train = train_data[:, 1:].T\n", "X_train = X_train / 255.0  # Normalize pixel values to [0, 1]\n", "y_train = train_data[:, 0]\n", "\n", "X_val = val_data[:, 1:].T\n", "X_val = X_val / 255.0  # Normalize pixel values to [0, 1]\n", "y_val = val_data[:, 0]"]}, {"cell_type": "code", "execution_count": 114, "id": "0e58194d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["X_train shape: (784, 33600)\n", "y_train shape: (33600,)\n", "X_val shape: (784, 8400)\n", "y_val shape: (8400,)\n"]}], "source": ["print(\"X_train shape:\", X_train.shape)\n", "print(\"y_train shape:\", y_train.shape)\n", "print(\"X_val shape:\", X_val.shape)\n", "print(\"y_val shape:\", y_val.shape)"]}, {"cell_type": "code", "execution_count": 115, "id": "a7723444", "metadata": {}, "outputs": [], "source": ["def initialize_parameters():\n", "    W1 = np.random.rand(10, 784) - 0.5\n", "    B1 = np.random.rand(10, 1) - 0.5\n", "    W2 = np.random.rand(10, 10) - 0.5\n", "    B2 = np.random.rand(10, 1) - 0.5\n", "\n", "\n", "    return W1, B1, W2, B2\n", "\n", "def ReLU(X):\n", "    return np.maximum(0, X)\n", "\n", "def softmax_calculator(Z):\n", "    return np.exp(Z) / np.sum(np.exp(Z), axis = 0)\n", "\n", "\n", "def forward_propagation(W1 , B1, W2, B2, X):\n", "    Z1 = W1.dot(X) + B1\n", "    A1 = ReLU(Z1)\n", "    Z2 = W2.dot(A1) + B2\n", "    A2 = softmax_calculator(Z2)\n", "    return Z1, A1, Z2, A2\n", "def one_hot_converter(Y):\n", "    one_hot_Y = np.zeros((Y.size, Y.max() +1))\n", "    one_hot_Y[np.arange(Y.size), Y] = 1\n", "    return one_hot_Y.T\n", "\n", "def backward_propagation(W1, B1, W2, B2, Z1, A1, Z2, A2, Y, X):\n", "    one_hot_Y = one_hot_converter(Y)\n", "    dZ2 = A2 - one_hot_Y\n", "    dW2 = 1/m * dZ2.dot(A1.T) \n", "    dB2 = 1/m * np.sum(dZ2)\n", "    dZ1 =  W2.T.dot(dZ2) * (Z1 > 0)  # Derivative of ReLU\n", "    dW1 = 1/m * dZ1.dot(X.T) \n", "    dB1 = 1/m * np.sum(dZ1)\n", "\n", "    return dW1, dB1, dW2, dB2\n", "\n", "def update_parameters(W1, B1, W2, B2, dW1, dB1, dW2, dB2, learning_rate=0.01):\n", "    W1 = W1 - learning_rate * dW1\n", "    B1 = B1 - learning_rate * dB1\n", "    W2 = W2 - learning_rate * dW2\n", "    B2 = B2 - learning_rate * dB2\n", "    return W1, B1, W2, B2\n", "\n", "def get_predictions(A2):\n", "    return np.argmax(A2, 0)\n", "def get_accuracy(predictions, Y):\n", "    return np.sum(predictions == Y) / Y.size\n", "\n", "\n", "def gradient_descent(X, Y , alpha, iterations):\n", "    W1, B1, W2, B2 = initialize_parameters()\n", "    for i in range(iterations):  \n", "        Z1, A1, Z2, A2 = forward_propagation(W1, B1, W2, B2, X)\n", "        dW1, dB1, dW2, dB2 = backward_propagation(W1, B1, W2, B2, Z1, A1, Z2, A2, Y, X)\n", "        W1, B1, W2, B2 = update_parameters(W1, B1, W2, B2, dW1, dB1, dW2, dB2, alpha)\n", "        if (i%20) == 0:\n", "            print(\"Iteration Number : \", i)\n", "            print(\"Accuracy = \", get_accuracy(get_predictions(A2), Y))\n", "    return W1, B1, W2, B2\n"]}, {"cell_type": "code", "execution_count": 116, "id": "a0eb08a8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Iteration Number :  0\n", "Accuracy =  0.11172619047619048\n", "Iteration Number :  20\n", "Accuracy =  0.15029761904761904\n", "Iteration Number :  40\n", "Accuracy =  0.25684523809523807\n", "Iteration Number :  60\n", "Accuracy =  0.3263095238095238\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[116]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m W1, B1, W2, B2 = \u001b[43mgradient_descent\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX_train\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my_train\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m0.1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m1000\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[115]\u001b[39m\u001b[32m, line 56\u001b[39m, in \u001b[36mgradient_descent\u001b[39m\u001b[34m(X, Y, alpha, iterations)\u001b[39m\n\u001b[32m     54\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(iterations):  \n\u001b[32m     55\u001b[39m     Z1, A1, Z2, A2 = forward_propagation(W1, B1, W2, B2, X)\n\u001b[32m---> \u001b[39m\u001b[32m56\u001b[39m     dW1, dB1, dW2, dB2 = \u001b[43mbackward_propagation\u001b[49m\u001b[43m(\u001b[49m\u001b[43mW1\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mB1\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mW2\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mB2\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mZ1\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mA1\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mZ2\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mA2\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mY\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mX\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     57\u001b[39m     W1, B1, W2, B2 = update_parameters(W1, B1, W2, B2, dW1, dB1, dW2, dB2, alpha)\n\u001b[32m     58\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m (i%\u001b[32m20\u001b[39m) == \u001b[32m0\u001b[39m:\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[115]\u001b[39m\u001b[32m, line 34\u001b[39m, in \u001b[36mbackward_propagation\u001b[39m\u001b[34m(W1, B1, W2, B2, Z1, A1, Z2, A2, Y, X)\u001b[39m\n\u001b[32m     32\u001b[39m dB2 = \u001b[32m1\u001b[39m/m * np.sum(dZ2)\n\u001b[32m     33\u001b[39m dZ1 =  W2.T.dot(dZ2) * (Z1 > \u001b[32m0\u001b[39m)  \u001b[38;5;66;03m# Derivative of ReLU\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m34\u001b[39m dW1 = \u001b[32m1\u001b[39m/m * \u001b[43mdZ1\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdot\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX\u001b[49m\u001b[43m.\u001b[49m\u001b[43mT\u001b[49m\u001b[43m)\u001b[49m \n\u001b[32m     35\u001b[39m dB1 = \u001b[32m1\u001b[39m/m * np.sum(dZ1)\n\u001b[32m     37\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m dW1, dB1, dW2, dB2\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["W1, B1, W2, B2 = gradient_descent(X_train, y_train, 0.1, 1000)"]}, {"cell_type": "code", "execution_count": null, "id": "c92ca190", "metadata": {}, "outputs": [], "source": ["import random\n", "val_index = random.randint(0, X_val.shape[1] - 1)\n", "Z1val, A1val, Z2val, A2val = forward_propagation(W1, B1, W2, B2, X_val[:, val_index, None])\n", "predictions = get_predictions(A2val)\n", "print(\"predicted label: \", predictions)\n", "print(\"actual label: \", y_val[val_index])\n", "image_array = X_val[:, val_index].reshape(28, 28)\n", "plt.imshow(image_array, cmap='gray')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "0aee42b1", "metadata": {}, "outputs": [], "source": ["Z1val, A1val, Z2val, A2val = forward_propagation(W1, B1, W2, B2, X_val)\n", "predictions = get_predictions(A2val)\n", "print(\"Validation Accuracy: \", get_accuracy(predictions, y_val))"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}